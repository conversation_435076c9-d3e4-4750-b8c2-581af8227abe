#  Licensed to the Apache Software Foundation (ASF) under one
#  or more contributor license agreements.  See the NOTICE file
#  distributed with this work for additional information
#  regarding copyright ownership.  The ASF licenses this file
#  to you under the Apache License, Version 2.0 (the
#  "License"); you may not use this file except in compliance
#  with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing,
#  software distributed under the License is distributed on an
#  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
#  KIND, either express or implied.  See the License for the
#  specific language governing permissions and limitations
#  under the License.
from random import choices, randint


class StringGenerator:
    _seed_letters: str
    _min_length: int
    _max_length: int

    def __init__(self, seed_letters: str, min_length: int, max_length: int):
        self._seed_letters = seed_letters
        self._min_length = min_length
        self._max_length = max_length

    def generate(self) -> str:
        rv_string_length = randint(self._min_length, self._max_length)
        randomized_letters = choices(self._seed_letters, k=rv_string_length)
        return "".join(randomized_letters)
