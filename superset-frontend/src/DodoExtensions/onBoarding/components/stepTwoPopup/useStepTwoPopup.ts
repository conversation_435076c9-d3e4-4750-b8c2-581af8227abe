import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { t } from '@superset-ui/core';
import { Role, UserFromEnum } from '../../types';
import { MIN_TEAM_NAME_LENGTH } from '../../consts';
import { getTeamName } from '../../utils/getTeamName';
import { getTeamSlug } from '../../utils/getTeamSlug';
import { finishOnBoarding } from '../../model/actions/finishOnBoarding';
import { ONBOARDING_TEAM_SEARCH_CLEAR } from '../../model/types/teamSearch.types';
import { getDodoRole } from '../../model/selectors/getDodoRole';

export const useStepTwoPopup = (onFinish: (value: boolean) => void) => {
  const [newTeam, setNewTeam] = useState<string | null>(null);
  const [existingTeam, setExistingTeam] = useState<any | null>(null);

  const [roles, setRoles] = useState<Array<Role>>([]);

  const [isTeamNameValid, setIsTeamNameValid] = useState(true);

  const dodoRole = useSelector(getDodoRole);
  const userFrom = dodoRole?.startsWith(UserFromEnum.Franchisee)
    ? UserFromEnum.Franchisee
    : UserFromEnum.ManagingCompany;

  const dispatch = useDispatch();

  const noTeam = useMemo(
    () => !existingTeam && (newTeam ?? '').trim().length < MIN_TEAM_NAME_LENGTH,
    [existingTeam, newTeam],
  );

  useEffect(() => {
    if (noTeam) {
      setRoles([]);
    }
  }, [noTeam]);

  const formatedTeamName = useMemo(() => {
    if (existingTeam) {
      return `${existingTeam.label}`;
    }
    if ((newTeam ?? '').trim().length >= MIN_TEAM_NAME_LENGTH) {
      const name = getTeamName(newTeam, userFrom);
      return `${name}`;
    }
    return 'no team';
  }, [existingTeam, newTeam, userFrom]);

  const formatedSlug = useMemo(() => {
    if (existingTeam) {
      return existingTeam.value;
    }
    if ((newTeam ?? '').trim().length >= MIN_TEAM_NAME_LENGTH) {
      return getTeamSlug(newTeam, userFrom);
    }
    return 'no slug';
  }, [existingTeam, newTeam, userFrom]);

  const submit = useCallback(() => {
    onFinish(true);
    dispatch(
      finishOnBoarding({
        userFrom,
        isNewTeam: !!newTeam,
        teamName: formatedTeamName,
        teamSlug: formatedSlug,
        roles,
      }),
    );
  }, [
    onFinish,
    dispatch,
    userFrom,
    newTeam,
    formatedTeamName,
    formatedSlug,
    roles,
  ]);

  const removeTeam = useCallback(
    (e: React.MouseEvent<HTMLElement>) => {
      setNewTeam(null);
      setExistingTeam(null);
      setRoles([]);
      setIsTeamNameValid(true);
      e.preventDefault();
      dispatch({ type: ONBOARDING_TEAM_SEARCH_CLEAR });
    },
    [dispatch],
  );

  const tagClosable = useMemo(
    () => !!existingTeam || !!newTeam,
    [existingTeam, newTeam],
  );

  const teamDescription = useMemo(() => {
    if (existingTeam) {
      return `${existingTeam.label} ${t('is a known team.')}`;
    }
    if ((newTeam ?? '').trim().length >= MIN_TEAM_NAME_LENGTH) {
      const name = getTeamName(newTeam, userFrom);
      return `${name} ${t('is a new team.')}`;
    }
    return '';
  }, [existingTeam, newTeam, userFrom]);

  return {
    newTeam,
    existingTeam,
    setRoles,
    setNewTeam,
    setExistingTeam,
    noTeam,
    roles,
    formatedTeamName,
    submit,
    removeTeam,
    tagClosable,
    teamDescription,
    isTeamNameValid,
    setIsTeamNameValid,
    userFrom,
  };
};
