import { useDispatch } from 'react-redux';
import { useCallback, useMemo } from 'react';
import { debounce } from 'lodash';
import { loadTeams } from '../../model/actions/loadTeams';
import { Role, UserFromEnum } from '../../types';
import { MAX_TEAM_NAME_LENGTH, SEARCH_TEAM_DELAY } from '../../consts';

// const reg = /^-?\d*(\.\d*)?$/; for numbers
const reg = /^-?[0-9a-zA-Z ]*(\.[0-9a-zA-Z ]*)?$/;

export const useTeam = ({
  userFrom,
  newTeam,
  existingTeam,
  isTeamNameValid,
  setNewTeam,
  setExistingTeam,
  setRoles,
  setIsTeamNameValid,
}: {
  userFrom: UserFromEnum | null;
  newTeam: string | null;
  existingTeam: any | null;
  isTeamNameValid: boolean;
  setNewTeam: (value: string | null) => void;
  setExistingTeam: (value: any | null) => void;
  setRoles?: (value: Array<Role>) => void;
  setIsTeamNameValid: (value: boolean) => void;
}) => {
  const dispatch = useDispatch();

  const debouncedLoadTeamList = useMemo(
    () =>
      debounce((value: string) => {
        if (value.length >= 3 && userFrom && reg.test(value)) {
          dispatch(loadTeams(userFrom, value));
        }
      }, SEARCH_TEAM_DELAY),
    [dispatch, userFrom],
  );

  const handleTeamChange: (value: string, option: any) => void = useCallback(
    (value, option) => {
      if (!!option.value && !!option.label) {
        setExistingTeam(option);
        setNewTeam(null);
        setRoles?.(option.roles);
      } else {
        if (value) {
          if (
            (isTeamNameValid && !reg.test(value)) ||
            (!isTeamNameValid && reg.test(value))
          ) {
            setIsTeamNameValid(reg.test(value));
          }

          if (value.length <= MAX_TEAM_NAME_LENGTH) {
            setNewTeam(value.toUpperCase());
          }
        } else {
          setNewTeam(null);
          setRoles?.([]);
          setIsTeamNameValid(true);
        }
        setExistingTeam(null);
      }
    },
    [
      isTeamNameValid,
      setExistingTeam,
      setIsTeamNameValid,
      setNewTeam,
      setRoles,
    ],
  );

  const teamNameOnAutoComplete = useMemo(() => {
    if (existingTeam) {
      return existingTeam.label;
    }
    if (newTeam) {
      return newTeam;
    }
    return null;
  }, [existingTeam, newTeam]);

  return {
    debouncedLoadTeamList,
    handleTeamChange,
    teamNameOnAutoComplete,
  };
};
