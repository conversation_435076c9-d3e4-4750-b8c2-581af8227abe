import { FC, memo } from 'react';
import { AutoComplete, Input } from 'antd';
import { t, styled } from '@superset-ui/core';
import { useSelector } from 'react-redux';
import { Role, UserFromEnum } from '../../types';
import { getTeamSearchData } from '../../model/selectors/getTeamSearchData';
import { useTeam } from './useTeam';

const StyledAutocomplete = styled(AutoComplete)<{ isTeamNameValid: boolean }>`
  width: 100%;
  .ant-input-affix-wrapper {
    border-color: ${({ isTeamNameValid, theme }) =>
      !isTeamNameValid && theme.colors.error.base} !important;
  }
  .ant-input {
    color: ${({ isTeamNameValid, theme }) =>
      !isTeamNameValid && theme.colors.error.base} !important;
  }
`;

type Props = {
  newTeam: string | null;
  existingTeam: any | null;
  setExistingTeam: (value: any | null) => void;
  setNewTeam: (value: string | null) => void;
  userFrom: UserFromEnum | null;
  setRoles?: (value: Array<Role>) => void;
  disabled?: boolean;
  isTeamNameValid: boolean;
  setIsTeamNameValid: (value: boolean) => void;
};

export const RequestFindTeam: FC<Props> = memo(
  ({
    newTeam,
    existingTeam,
    setNewTeam,
    setExistingTeam,
    userFrom,
    setRoles,
    disabled,
    isTeamNameValid,
    setIsTeamNameValid,
  }) => {
    const { teamsIsLoading, teams } = useSelector(getTeamSearchData);

    const { debouncedLoadTeamList, handleTeamChange, teamNameOnAutoComplete } =
      useTeam({
        userFrom,
        newTeam,
        existingTeam,
        isTeamNameValid,
        setNewTeam,
        setExistingTeam,
        setRoles,
        setIsTeamNameValid,
      });

    return (
      <StyledAutocomplete
        isTeamNameValid={isTeamNameValid}
        value={teamNameOnAutoComplete}
        options={teams}
        onSearch={isTeamNameValid ? debouncedLoadTeamList : undefined}
        onChange={handleTeamChange}
        disabled={disabled}
        onClear={() => setIsTeamNameValid(true)}
      >
        <Input.Search
          placeholder={t('enter team name')}
          loading={teamsIsLoading}
          allowClear
          enterButton
          size="middle"
        />
      </StyledAutocomplete>
    );
  },
);
