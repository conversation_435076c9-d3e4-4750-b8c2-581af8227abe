import { FC, useState } from 'react';
import { Col, RadioChangeEvent, Row, Select } from 'src/components';
import { Typography } from 'antd';
import { styled, t } from '@superset-ui/core';
import { useSelector } from 'react-redux';
import { Radio } from 'src/components/Radio';
import { useForm } from 'antd/lib/form/Form';
import Modal from '../../../../components/Modal';
import { Form, FormItem } from '../../../../components/Form';
import { StepOnePopupDto } from './stepOnePopup.dto';
import { Input } from '../../../../components/Input';
import Loading from '../../../../components/Loading';
import { ButtonWithTopMargin } from '../styles';
import { getOnboardingStartedTime } from '../../model/selectors/getStepOneData';
import { getOnboardingStepOneUpdating } from '../../model/selectors/getOnboardingStepOneUpdating';
import { UserFromEnum } from '../../types';
import {
  FRANCHISEE_ROLES_OPTIONS,
  MANAGING_COMPANY_ROLES_OPTIONS,
} from '../../consts';

const userFromOptions = [
  {
    label: t(UserFromEnum.ManagingCompany),
    value: UserFromEnum.ManagingCompany,
  },
  { label: t(UserFromEnum.Franchisee), value: UserFromEnum.Franchisee },
];

const Wrapper = styled.div`
  padding: 1.5rem;
`;

type Props = {
  onClose: () => void;
  onNextStep: (dto: StepOnePopupDto) => void;
};

export const StepOnePopup: FC<Props> = ({ onNextStep, onClose }) => {
  const [, forceRender] = useState(false);
  const { firstName, lastName, email } = useSelector(getOnboardingStartedTime);
  const isUpdating = useSelector(getOnboardingStepOneUpdating);
  const { Title, Paragraph } = Typography;
  const [form] = useForm();

  const toggleUserFrom = ({ target: { value } }: RadioChangeEvent) => {
    form.setFieldsValue({ userFrom: value });
    form.setFieldsValue({ dodoRole: undefined });
    forceRender(prev => !prev);
  };

  return (
    <Modal
      show
      title={t('You are welcome to Superset')}
      hideFooter
      onHide={onClose}
      width="1000px"
    >
      <Wrapper>
        <Row gutter={32}>
          <Col span={14}>
            <Title level={3}>{t('Tell us more about yourself')}</Title>
            <Paragraph type="secondary">
              {t(
                'All the data is from Dodo IS. Please enter your team or role. It helps to proceed your request.',
              )}
            </Paragraph>
            <Form
              name="stepOne"
              onFinish={onNextStep}
              autoComplete="off"
              layout="vertical"
              form={form}
              initialValues={{
                firstName,
                lastName,
                email,
                userFrom: UserFromEnum.ManagingCompany,
                dodoRole: undefined,
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <FormItem label={t('First name')} name="firstName">
                    <Input disabled />
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem label={t('Last name')} name="lastName">
                    <Input disabled />
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <FormItem label="email" name="email">
                    <Input disabled />
                  </FormItem>
                </Col>
              </Row>

              <Typography.Title level={5}>
                {t('Are you a franchisee or from a Managing Company?')}
              </Typography.Title>

              <Col span={24}>
                <FormItem name="userFrom">
                  <Radio.Group
                    onChange={toggleUserFrom}
                    options={userFromOptions}
                  />
                </FormItem>
              </Col>
              <Row>
                <Col span={24}>
                  <FormItem
                    label={t('Role in Dodo Brands')}
                    name="dodoRole"
                    rules={[
                      {
                        required: true,
                        message: t('Please select your role in Dodo Brands!'),
                      },
                    ]}
                  >
                    <Select
                      options={
                        form.getFieldValue('userFrom') ===
                        UserFromEnum.Franchisee
                          ? FRANCHISEE_ROLES_OPTIONS
                          : MANAGING_COMPANY_ROLES_OPTIONS
                      }
                    />
                  </FormItem>
                </Col>
              </Row>
              <ButtonWithTopMargin type="primary" htmlType="submit">
                {t('Next step')}
              </ButtonWithTopMargin>
            </Form>
          </Col>

          <Col span={10}>
            <img
              src="/static/assets/images/onBoardingStepOne.png"
              alt="onBoardingStepOne"
              width="100%"
            />
          </Col>
        </Row>
      </Wrapper>
      {isUpdating && <Loading />}
    </Modal>
  );
};
