import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { StepOnePopupDto } from '../components/stepOnePopup/stepOnePopup.dto';

import {
  clearStorageInitialByUser,
  getOnboardingStorageInfo,
  updateStorageTimeOfTheLastShow,
} from '../utils/localStorageUtils';
import { initOnboarding } from '../model/actions/initOnboarding';
import { getIsOnboardingFinished } from '../model/selectors/getIsOnboardingFinished';
import { getOnboardingStartedTime } from '../model/selectors/getOnboardingStartedTime';
import { stepOneFinish } from '../model/actions/stepOneFinish';

// hardcode to stop show onboarding popup

const oneDayPassed = (date?: Date): boolean => false;

// const oneDayPassed = (date?: Date): boolean => {
//   const ONE_DAY_LATER_DISTANCE = 24 * 60 * 60 * 1000;
//
//   if (date) {
//     if (new Date(Number(date) + ONE_DAY_LATER_DISTANCE) >= new Date()) {
//       return false;
//     }
//   }
//   return true;
// };

export const useOnboarding = () => {
  const [inited, setInited] = useState(false);
  const [step, setStep] = useState<number | null>(null);

  const step2PassedRef = useRef<null | boolean>();

  const dispatch = useDispatch();
  const isOnboardingFinished = useSelector(getIsOnboardingFinished);
  const onboardingStartedTime = useSelector(getOnboardingStartedTime);

  const { theTimeOfTheLastShow, initialByUser } = getOnboardingStorageInfo();

  useEffect(() => {
    // @ts-ignore
    dispatch(initOnboarding()).then(() => setInited(true));
  }, [dispatch]);

  useEffect(() => {
    if (!inited) {
      return;
    }

    if (isOnboardingFinished && initialByUser) {
      setStep(3);
      return;
    }
    if (
      onboardingStartedTime &&
      (oneDayPassed(theTimeOfTheLastShow) || initialByUser)
    ) {
      setStep(2);
      return;
    }

    if (oneDayPassed(theTimeOfTheLastShow) || initialByUser) {
      setStep(1);
    }
  }, [
    inited,
    initialByUser,
    isOnboardingFinished,
    onboardingStartedTime,
    theTimeOfTheLastShow,
  ]);

  const closeOnboarding = useCallback(() => {
    updateStorageTimeOfTheLastShow();
    clearStorageInitialByUser();
    setStep(null);
  }, []);

  const toStepTwo = async (stepOneDto: StepOnePopupDto) => {
    dispatch(stepOneFinish(stepOneDto.dodoRole));
  };

  const setStep2Passed = useCallback(() => {
    step2PassedRef.current = true;
  }, []);

  const setStep3Passed = useCallback(() => {
    clearStorageInitialByUser();
    step2PassedRef.current = null;
    setStep(null);
  }, []);

  return {
    step,
    closeOnboarding,
    toStepTwo,
    setStep2Passed,
    setStep3Passed,
  };
};
